// Optimized prerender function for Node.js runtime
// Updated: 2025-06-08 - Performance-optimized implementation for 95+ speed score

const BOT_USER_AGENTS = [
  'googlebot', 'bingbot', 'facebookexternalhit', 'twitterbot',
  'linkedinbot', 'discord', 'whatsapp', 'telegram', 'slurp',
  'duckduckbot', 'baiduspider', 'yandexbot', 'pinterestbot',
  'redditbot', 'skype', 'applebot'
];

const META_TAGS = {
  title: 'Econic Media | Luxury Web Design & Professional Product Photography',
  description: 'Transform your brand with luxury web design and professional product photography. Modern, responsive websites and stunning product visuals that convert visitors into customers.',
  keywords: 'luxury web design, product photography, professional photography, modern websites, responsive design, brand transformation, e-commerce photography, web development',
  ogImage: 'https://www.econicmedia.pro/websitepreview.png',
  twitterImage: 'https://www.econicmedia.pro/websitepreview.png',
};

function isBotUserAgent(userAgent, acceptHeader) {
  if (!userAgent) return false;
  const ua = userAgent.toLowerCase();

  // Check against known bot patterns
  const isKnownBot = BOT_USER_AGENTS.some(bot => ua.includes(bot));
  if (isKnownBot) return true;

  // Heuristic: If UA does not contain 'Mozilla' and Accept header includes 'text/html', treat as crawler
  const isMozillaBased = ua.includes('mozilla');
  const acceptsHtml = acceptHeader?.includes('text/html') || false;

  if (!isMozillaBased && acceptsHtml) {
    return true;
  }

  return false;
}

function generateOptimizedHTML() {
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${META_TAGS.title}</title>
  <meta name="description" content="${META_TAGS.description}">
  <meta name="keywords" content="${META_TAGS.keywords}">
  <meta name="author" content="Econic Media">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://www.econicmedia.pro">
  <meta property="og:title" content="${META_TAGS.title}">
  <meta property="og:description" content="${META_TAGS.description}">
  <meta property="og:image" content="${META_TAGS.ogImage}">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:image:alt" content="Econic Media - Modern web design and professional product photography">
  <meta property="og:site_name" content="Econic Media">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://www.econicmedia.pro">
  <meta property="twitter:title" content="${META_TAGS.title}">
  <meta property="twitter:description" content="${META_TAGS.description}">
  <meta property="twitter:image" content="${META_TAGS.twitterImage}">
  <meta property="twitter:image:alt" content="Econic Media - Modern web design and professional product photography">

  <!-- Additional SEO -->
  <link rel="canonical" href="https://www.econicmedia.pro">
  <meta name="robots" content="index, follow">
  <meta name="googlebot" content="index, follow">

  <!-- Favicon -->
  <link rel="icon" href="/newlogofinal.png" type="image/png">

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Econic Media",
    "url": "https://econicmedia.pro",
    "logo": "https://econicmedia.pro/newlogofinal.png",
    "description": "${META_TAGS.description}",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>"
    }
  }
  </script>
</head>
<body>
  <main>
    <section>
      <h1>Transform Your Brand with Luxury Web Design</h1>
      <p>We create stunning, modern websites and professional product photography that convert visitors into customers. Our luxury design approach ensures your brand stands out in today's competitive market.</p>
    </section>

    <section>
      <h2>Our Services</h2>
      <div>
        <h3>Web Design & Development</h3>
        <p>Modern, responsive websites built with cutting-edge technology. From concept to launch, we create digital experiences that engage and convert.</p>
      </div>
      <div>
        <h3>Product Photography</h3>
        <p>Professional product photography that showcases your products in the best light. High-quality images that drive sales and enhance your brand image.</p>
      </div>
    </section>
  </main>

  <footer>
    <p>&copy; 2025 Econic Media. All rights reserved.</p>
    <p>Professional web design and product photography services.</p>
  </footer>
</body>
</html>`;
}

module.exports = async function handler(req, res) {
  try {
    const userAgent = req.headers['user-agent'] || '';
    const acceptHeader = req.headers['accept'] || '';

    // Enhanced bot detection with heuristics
    const isBot = isBotUserAgent(userAgent, acceptHeader);

    // Add debug logging for deployment verification
    console.log('Prerender request:', {
      userAgent: userAgent.substring(0, 100), // Truncate for logs
      isBot,
      timestamp: new Date().toISOString()
    });

    if (!isBot) {
      return res.redirect(302, '/');
    }

    const html = generateOptimizedHTML();

    // Optimized headers for performance
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.setHeader('Cache-Control', 'public, max-age=3600, s-maxage=3600, stale-while-revalidate=300');
    res.setHeader('X-Prerender', 'true');
    res.setHeader('X-Served-By', 'prerender-nodejs');
    res.setHeader('X-Robots-Tag', 'index, follow');

    res.status(200).send(html);

  } catch (error) {
    console.error('Prerender error:', error);

    // Fallback HTML for errors
    const fallbackHTML = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${META_TAGS.title}</title>
  <meta name="description" content="${META_TAGS.description}">
  <meta property="og:image" content="${META_TAGS.ogImage}">
</head>
<body>
  <h1>Econic Media</h1>
  <p>Luxury web design and professional product photography.</p>
</body>
</html>`;

    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.setHeader('X-Prerender', 'true');
    res.setHeader('X-Prerender-Error', 'true');
    res.status(200).send(fallbackHTML);
  }
}
