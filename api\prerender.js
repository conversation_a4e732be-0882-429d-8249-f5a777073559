// Simplified prerender function for Edge Runtime
// Updated: 2025-06-08 - Minimal implementation for deployment success

const BOT_USER_AGENTS = [
  'googlebot', 'bingbot', 'facebookexternalhit', 'twitterbot',
  'linkedinbot', 'discord', 'whatsapp', 'telegram'
];

const META_TAGS = {
  title: 'Econic Media | Luxury Web Design & Professional Product Photography',
  description: 'Transform your brand with luxury web design and professional product photography.',
  ogImage: 'https://www.econicmedia.pro/websitepreview.png',
  twitterImage: 'https://www.econicmedia.pro/websitepreview.png',
};

function isBotUserAgent(userAgent) {
  if (!userAgent) return false;
  const ua = userAgent.toLowerCase();
  return BOT_USER_AGENTS.some(bot => ua.includes(bot));
}

function generateHTML() {
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${META_TAGS.title}</title>
  <meta name="description" content="${META_TAGS.description}">
  <meta property="og:title" content="${META_TAGS.title}">
  <meta property="og:description" content="${META_TAGS.description}">
  <meta property="og:image" content="${META_TAGS.ogImage}">
  <meta property="og:url" content="https://www.econicmedia.pro">
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:title" content="${META_TAGS.title}">
  <meta property="twitter:description" content="${META_TAGS.description}">
  <meta property="twitter:image" content="${META_TAGS.twitterImage}">
</head>
<body>
  <h1>Econic Media</h1>
  <p>Luxury web design and professional product photography.</p>
</body>
</html>`;
}

module.exports = async function handler(req, res) {
  try {
    const userAgent = req.headers['user-agent'] || '';

    if (!isBotUserAgent(userAgent)) {
      return res.redirect(302, '/');
    }

    const html = generateHTML();

    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.setHeader('Cache-Control', 'public, max-age=3600');
    res.setHeader('X-Prerender', 'true');
    res.status(200).send(html);

  } catch (error) {
    res.status(500).send('Error');
  }
}
